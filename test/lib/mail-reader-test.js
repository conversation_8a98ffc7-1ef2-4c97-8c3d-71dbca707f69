/**
 * The MIT License (MIT)
 *
 * Copyright (c) 2014 Whiteout Networks GmbH
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy of
 * this software and associated documentation files (the "Software"), to deal in
 * the Software without restriction, including without limitation the rights to
 * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
 * the Software, and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

import {expect} from 'test';
import * as mailreader from 'lib/mail-reader';
import {Uint8Array2str} from 'lib/util';

describe('mail-reader', () => {
  describe('parse', () => {
    it('should parse text', () => {
      const bodyParts = mailreader.parse([{
        type: 'text',
        raw: 'Content-Type: text/plain; charset=ISO-8859-1\r\n\r\nasdasd\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('string');
      expect(bodyParts[0].content).to.equal('asdasd');
    });

    it('should parse empty text', () => {
      const bodyParts = mailreader.parse([{
        type: 'text',
        raw: 'Content-Type: text/plain\r\nContent-Transfer-Encoding: 7bit\r\n\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('string');
      expect(bodyParts[0].content).to.equal('');
    });

    it('should parse html', () => {
      const bodyParts = mailreader.parse([{
        type: 'html',
        raw: 'Content-Type: text/html; charset="UTF-8"\r\n\r\n<pre>PGP MESSAGE</pre>\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('string');
      expect(bodyParts[0].content).to.equal('<pre>PGP MESSAGE</pre>');
    });

    it('should parse empty html', () => {
      const bodyParts = mailreader.parse([{
        type: 'html',
        raw: 'Content-Type: text/html; charset="UTF-8"\r\n\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('string');
      expect(bodyParts[0].content).to.equal('');
    });

    it('should parse attachment', () => {
      const bodyParts = mailreader.parse([{
        type: 'attachment',
        raw: 'Content-Type: text/plain; name="nyS76EP.jpg"\r\nContent-Disposition: attachment; filename="nyS76EP.jpg"\r\nContent-Id: <9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878>\r\n\r\nasdasd\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('uint8array');
      expect(bodyParts[0].content.length).to.equal(7);
      expect(Uint8Array2str(bodyParts[0].content)).to.equal('asdasd\n');
      expect(bodyParts[0].raw).to.not.exist;
      expect(bodyParts[0].filename).to.equal('nyS76EP.jpg');
      expect(bodyParts[0].mimeType).to.exist;
      expect(bodyParts[0].id).to.equal('9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878');
    });

    it('should parse attachment without content-id', () => {
      const bodyParts = mailreader.parse([{
        type: 'attachment',
        raw: 'Content-Type: text/plain; name="nyS76EP.jpg"\r\nContent-Disposition: attachment; filename="nyS76EP.jpg"\r\n\r\nasdasd\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('uint8array');
      expect(bodyParts[0].content.length).to.equal(7);
      expect(Uint8Array2str(bodyParts[0].content)).to.equal('asdasd\n');
      expect(bodyParts[0].raw).to.not.exist;
      expect(bodyParts[0].filename).to.equal('nyS76EP.jpg');
      expect(bodyParts[0].mimeType).to.exist;
      expect(bodyParts[0].id).to.not.exist;
    });

    it('should parse attachment without content-type', () => {
      const bodyParts = mailreader.parse([{
        type: 'attachment',
        raw: 'Content-Disposition: attachment; filename="nyS76EP.jpg"\r\nContent-Id: <9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878>\r\n\r\nasdasd\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('uint8array');
      expect(bodyParts[0].content.length).to.equal(7);
      expect(Uint8Array2str(bodyParts[0].content)).to.equal('asdasd\n');
      expect(bodyParts[0].raw).to.not.exist;
      expect(bodyParts[0].filename).to.equal('nyS76EP.jpg');
      expect(bodyParts[0].mimeType).to.exist;
      expect(bodyParts[0].id).to.equal('9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878');
    });

    it('should parse attachment without filename', () => {
      const bodyParts = mailreader.parse([{
        type: 'attachment',
        raw: 'Content-Disposition: attachment\r\nContent-Type: image/jpeg\r\nContent-Id: <9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878>\r\nContent-Transfer-Encoding: base64\r\n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\r\nAAAAAAAAAAAAAH//2Q==\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('uint8array');
      // in emailjs-mime-parser v1.0 'Content-Disposition: attachment' header was not required to get this parsed correctly. See also: https://github.com/emailjs/emailjs-mime-parser/issues/18
      expect(bodyParts[0].content.length).to.equal(29197);
      expect(bodyParts[0].raw).to.not.exist;
      expect(bodyParts[0].filename).to.equal('attachment');
      expect(bodyParts[0].mimeType).to.equal('image/jpeg');
      expect(bodyParts[0].id).to.equal('9EC769A2-4AF4-4D47-A0AB-96CEA7CA5878');
    });

    it('should parse empty attachment', () => {
      const bodyParts = mailreader.parse([{
        type: 'attachment',
        raw: 'Content-Disposition: attachment; filename="nyS76EP.jpg"\r\n\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('uint8array');
      expect(bodyParts[0].content.length).to.equal(0);
      expect(Uint8Array2str(bodyParts[0].content)).to.equal('');
      expect(bodyParts[0].raw).to.not.exist;
      expect(bodyParts[0].filename).to.equal('nyS76EP.jpg');
      expect(bodyParts[0].mimeType).to.exist;
    });

    it('should parse encrypted', () => {
      const bodyParts = mailreader.parse([{
        type: 'encrypted',
        raw: 'Content-Type: multipart/encrypted;\r\n boundary="----sinikael-?=_3-13993193614470.10911058727651834"\r\nContent-Description: OpenPGP encrypted message\r\nContent-Transfer-Encoding: base64\r\n\r\nVGhpcyBpcyBhbiBPcGVuUEdQL01JTUUgZW5jcnlwdGVkIG1lc3NhZ2Uu\r\n\r\n------sinikael-?=_3-13993193614470.10911058727651834\r\nContent-Type: application/pgp-encrypted\r\nContent-Description: PGP/MIME Versions Identification\r\nContent-Transfer-Encoding: base64\r\n\r\nVmVyc2lvbjogMQ==\r\n------sinikael-?=_3-13993193614470.10911058727651834\r\nContent-Type: application/octet-stream; name=encrypted.asc\r\nContent-Description: OpenPGP encrypted message\r\nContent-Disposition: inline; filename=encrypted.asc\r\nContent-Transfer-Encoding: base64\r\n\r\nLS0tLS1CRUdJTiBQR1AgTUVTU0FHRS0tLS0tDQpWZXJzaW9uOiBPcGVuUEdQLmpzIHYwLjUuMQ0K\r\nQ29tbWVudDogaHR0cDovL29wZW5wZ3Bqcy5vcmcNCg0Kd2NCTUEyelM1VjlZSHZXNEFRZitPOCtk\r\nekQ2VHgyRE54ZWxuY0FiZlZURkVyRXRKNEJZbUNvRi9UQ3FiCk9JT2t4NExrYkNTcGpSR0twdWFq\r\nNk5sUHJQRFhzSEdQT1kyUEd0cmxYT3ZPdVU3M21aSU1odGpEU1NtRApVWkhyS0FpMUFsb2tEQ29T\r\nUkFDT2ZQV1BTNGVkT1VHUGtLSW9xdGl1TDdwT0grVmFKMk9ZcGp0bnJjaDAKRzlVRW1tMnFXdWg2\r\nVEczbnFFNE5jazF2WWwrTVFQTDNFNHU5eU9Jd2Y0TXQ1TUFNWTJIT3BldVg0eFlPCnZOZ2FFNUtj\r\nZ291VFVqeXd1bGtQV0JVSUpONmRENSsyUGdjK2Z4Q2JodnU0MTJ2SkNJRjFmYjJWMjkrNAo0dHZi\r\nZVAvajczbWRlZ3lyTmoxZWgzUkx0cWpZZHdoRVlqeXU5eVZsUXZPSXFMMDNYZ1VvcStDTW5rQTcK\r\nVGNIQVRBTnMwdVZmV0I3MXVBRUgrd1NvY3g4ZTJ2Z0tkZmJMZmJweHhmb0dvVVF1WTcwMDFkZUhv\r\nZzQ4Clh5ejZ3VjJXeE11MjVQcXhUSzVjNEhHcmlaZXc3cnc4QzlmOWJxUDFhaTZjUTlLM2J5YjNW\r\ndkh3Uk1ncgo5TnR2NEIwYkJoSHh4Y3hDMzI4MUJNelc1QXM2a3RkdktaMVBpcjNuWEtCOGNhcHlM\r\nNE0ra0xiMnlIWVUKT0tEaXI0cHBKUnFBOTg5TEY3NUc1bldSaVk4cCtaejZqdVRSdmJnRFZ6RUVT\r\nb2EycFVFWWU1ZHdFdUtlCm5ET0tyY2Z2OFVWSm1tWkptTjFnZ1o0Uk12dHNoa1l3Zkt6SzJ5R2dJ\r\nZlRqV2cyd1FTUlNJVmo2bHpJRQpVUzJNanhqOFJYa2poTk5aQlBmM3FmM2tBeWh2cUN2YjdoYUxR\r\nTFg0bG9kVXpPRnlhWFFsRUZQNWpOaVUKQ09mU3hhMEI0cThIakR6eFZUemVmR0tlNUpWMVZWNGRp\r\nN2wyZFhpbkZYbGltOHZzNDJySHAvZC9yM1FwCkQ2ZDRmeHVXdjcvQlVDWTBCc2h6L3FCOWprVzJm\r\nQ3pFLzB6TGo2bUNMUzZXZERiWUsweExuNjdKcjRuSgpGbWYwckppYWJleGwvcEhGbWpZdnJzSTNi\r\nMFRHdnExTWg2VzdkbGxiR1MrNklJNFVtOWg1NXBZQWhwVHcKSmJLLzFiamN4T1RTR1VMMkZ2WDll\r\nUmhOWEwzWU4xVFZMeFhvMVZ0WUhEcm9QSUdYclh1QzM5a09ybW1hCmpaakI3RXlVS3JXVEloVExM\r\nZ3M4bzlleVpLNkRkVXYxM201UnZFYWkySEFOKzNaaldFNEZ5TlRJeU9aTQpCcnBKWHlnaGt0VFFt\r\nSmNyNlU1c3llS2F2R2tlMEwvQ29nVVBBYzZkSDRLZXlJZ2FnSVhLRWd4aUtPSGcKdGZxSVBHWi8y\r\neFZhdndJbU5wUk5CeFNuSUVSd1htZ0syenYwZmVjak41ckZWVGFLNElWMWdRaXFldk4wCnlZY0w3\r\nUVNnMk80K2pIYkFxejIxSGFTSVo2cnZXS0xDWXlvNkUvc3BsWnpGbVBZTlhFdEEvbzR3U1ZlRwpQ\r\ndDd6YkNQdy96Z2JXK2NFb3B3VzVJTTZweVVQTnFTUzNIZExMZ0Z5SDBJckg4a3Z6QkMyK3lWNHZq\r\nU0gKZ2ZYOTMydElncXEwU0pXOXdEbFdMVGhXOU8rc3l0NnFYcElqSnI3ZDFOME5EbVJPdzVTMnJO\r\nV01UaFNpCkQxMmRMWGVPbnExNW94V3g3ejdJeWZ6WmdQcHlQVjJRaWJmd0VHR3E3ek5ZZEFYanJN\r\nVHIyTHJQTCt5agpqTTFCRmw4L2xBaW1nQ2hXMFluMndFckd2TjdpSTAzNHFYTjd6MUlQOGNwOUQv\r\nSWpDZjJLMVZzUzIvY0MKYjhlalpsWmRmQkQxZk9LaldabklyUGlWSFJRQ2hhSndPRHc3dm1mOU55\r\ndU9MMXpUeFpKdlVPUTBCSVhsCkVlOVRKeDR5VEpBa1FYbEMyZGR2N3NHZzEweGNldm9NeGdmODRv\r\nWnIxL3c5MU5LQlMxSDJuQUZ4RU9sZAprMWlSRnVybGc3K1BVZEY3MVYrdHY2MUNYOTJjQ2ZkYXJh\r\nTWdVU2Rzd2xiQjV1UmYxMUV2NUhXN3JGNm4KTE5idVVXbWdVMkttcmZoR2d2TENIaGp3Z0EvV3ZQ\r\nTXRsZHNyUHphZWR0Q0xIc1hPQXZScjdaS3FQRUJkCllERE5QZ0dUTjFqZVdOZHZEUGlnQXhLM3pC\r\nSHh2RW9uUHdMVTZ6Wk5vUlB2eUlyY3l4M3ZOU25CSWxnYwpYb3VYTUREbjlUdnlqTGxZblZqd3pS\r\nRnRkWk54R29ScmVUK1V3dXl1T0dTd1U2aGJxaGVEL1ZPeFRCNVYKYXp6T25HV0kwOHZNKzAySHM0\r\na2ZoSmllVGk0dW5XaWhEaEM2UGFYV2dqNUpMVG5pcytGaHBJMUZSWTBHCnRsbkROZnRXOUJHYzdq\r\nVXUvRWxRRGVKQ2lKUlFpSjh2UnhKaXpGc0FpbTZuZm1DTmN5M2NLZjBmMEVUOQpjcjZMRURCQXJn\r\ncC92OHZuc3lNb1dzUEhBc1UyRUM2ZTdmOC9zLzVzWDJNMDBvRnMyU283N3hBZFdPTi8KYWlhWGox\r\nUDQwWS9HcE5ER0JiYi9NbThnSlo3YmVXREJSNVdKVVhTZ09pVUs4L3UrekR3UXgwM0JJZDVGCmwz\r\nOUZlQUZkRlBsY0NYT3hFY0NqejNrYVFUSVJJblZxZEtqbzFvb3pHWmVPalR0MTFpWGFkQmtyNE9R\r\nYQpJclI5UVkwcXFGQWFKT0hiTGY2ZXZYYXUrUzU4ZVZPLzVOZjJLbmhIWWt4OGpINmwwdFg0clY0\r\nYkF0cUkKL3ZBOGdZUHhyQW1jc0VCbTBTYTNKZnhKM3BzbUxJOEhIdHlyUjRLMitmdk1DdGo5UzQ2\r\neVNMZWJibDNzCmZsK3QwSXI5bWlxWnl1RXNwN29rVWxlRTkrQlhwVGVMak9sN29JeGpiY0NYeUhE\r\neTFDV3dtVmlzZDB1SQpTMkx1M3FhTTVBb2hVek9mdU1GL0JDVTJNTm8zSm9iZUpWeDZDbnBBakw3\r\nZVpJalRuMEtnNmc4QTZjQzgKamVkUEFBSkNVV0dYUk1sSGxBMmxHbnFzL3ArNUtoaTFJbllVc2N3\r\nRUVDSDN2ekhpN2xiRndzYUhTU1pZCnlTcTllQXl4M2ZKT1dtaGlxUHF6d25sQ29Idnh6bVliVWpr\r\nU0tYSWJ1SjRZd1p3K1RGMUxVbHJaV2FOTQpsUEtBd3ZDMWtiZ1FjaldLVXhpNGM3cERiWFRBNEN6\r\naWRUUUVibmdrWWxjZFJURTFHcUF3aHhBTzJhVksKdUxOZS9BRXlhWDVuQ1JiUHZKWEloUS9aZUJC\r\nYW1Dd2NPMEkzaUVBR2V5V0JzOWRabEk1RDZUc0hIbDR0CjZIdmlzcW5CWm9SM2VObVZMbFppak9M\r\nUnI3eWNGY0pCcndoZE9yM0xiN2hRYWRUS1REVk15MHBKanhTaApzTzE4c1NkeGNmWU9oVWc5ZHVx\r\nUkdLU3MwaFJjNStZT0VLTElqUDd5Qlo5VHZpNWJWYjE0TzlDSm9DbDgKT1ZjRk1iQTFnamlXV0Jt\r\naWh3Tm15T0hOaWFabUFTMHphODJZY2lGQ1ptdndwNzJFd0hqMUwvV0w5K2V1CmpqUkdqYzFKNXBD\r\nNnpld1hqbjE1TE9kWDZ5UHdkTHZjYnVyYzY4Q1QxbkFzSTRZSGZOQ1pJWEk4bzBzQQp3dUdKbjhy\r\nRExxVUwxcGJhRndteE4wTzRKeWRIbHJZWG0rbmpjUUE1Z0wwTE1UaFY1dDFFTUZBdnBoc0oKWVpG\r\nSU84TjZRZFFNVTdXZmJzZFhFMXBwYlRkM2JRRlFrb0IvaDNtRg0KPWtrR3YNCi0tLS0tRU5EIFBH\r\nUCBNRVNTQUdFLS0tLS0NCg==\r\n------sinikael-?=_3-13993193614470.10911058727651834--\r\n'
      }]);
      expect(bodyParts[0].content).to.not.be.empty;
      expect(bodyParts[0].content).to.match(/^-----BEGIN PGP MESSAGE-----/);
      expect(bodyParts[0].raw).to.not.exist;
    });

    it('should parse empty encrypted', () => {
      const bodyParts = mailreader.parse([{
        type: 'encrypted',
        raw: 'Content-Type: multipart/encrypted;\r\n boundary="----sinikael-?=_3-13993193614470.10911058727651834"\r\nContent-Description: OpenPGP encrypted message\r\nContent-Transfer-Encoding: base64\r\n\r\n------sinikael-?=_3-13993193614470.10911058727651834--\r\n'
      }]);
      expect(bodyParts[0].content).to.be.a('array');
      expect(bodyParts[0].content).to.eql([]);
      expect(bodyParts[0].raw).to.not.exist;
    });

    it('should parse signed', () => {
      const bodyParts = mailreader.parse([{
        type: 'signed',
        raw: 'Content-Type: multipart/signed; boundary="Apple-Mail=_C94D8F86-2AA4-4D9A-A975-F51C8A2937B6"; protocol="application/pgp-signature"; micalg=pgp-sha512\r\n\r\n--Apple-Mail=_C94D8F86-2AA4-4D9A-A975-F51C8A2937B6\r\nContent-Transfer-Encoding: 7bit\r\nContent-Type: text/plain;\r\n    charset=us-ascii\r\n\r\nthis is some signed stuff!\r\n\r\n--Apple-Mail=_C94D8F86-2AA4-4D9A-A975-F51C8A2937B6\r\nContent-Transfer-Encoding: 7bit\r\nContent-Disposition: attachment;\r\n    filename=signature.asc\r\nContent-Type: application/pgp-signature;\r\n    name=signature.asc\r\nContent-Description: Message signed with OpenPGP using GPGMail\r\n\r\n-----BEGIN PGP SIGNATURE-----\r\nComment: GPGTools - https://gpgtools.org\r\n\r\niQEcBAEBCgAGBQJTaJgoAAoJEOHUm+Va/GWKreEIAI9qgTBR1SWciKQXduY2ZyY1\r\n3ymKequbFKyoG6gytrIfeAeMJrTZiySXNvOHMlm852fE0vQFWNXtVf2XW0wp8gHL\r\n9X8rpaKtArQHNXWgWN/23+Ea1A0GsyMaxRQxJgj62BEsQsnGUJDgWhq6T5SDZA+h\r\n1ihy12Xvh4F4P//Nt8az2EmWLCv4KbzGp6LVS5jqVxPncuO5mKYZB3yupXnV2nKA\r\nrijmxCTaTJM2tTcTucxNR7hiYTjY6kCpmaTGg9Aq1iy8+hahZ/ZJndzrIMcg+VEA\r\nclbOS6qREijrtuUDLiK58j4w41vRsOmbMOyGQEYNJ7cXQ793/qDPetY4W2ZtRLk=\r\n=iMlU\r\n-----END PGP SIGNATURE-----\r\n\r\n--Apple-Mail=_C94D8F86-2AA4-4D9A-A975-F51C8A2937B6--\r\n'
      }]);
      expect(bodyParts[0].signedMessage).to.exist;
      expect(bodyParts[0].signedMessage).to.match(/^Content-Transfer-Encoding/);
      expect(bodyParts[0].signedMessage).to.match(/this is some signed stuff!/);
      expect(bodyParts[0].signature).to.exist;
      expect(bodyParts[0].signature).to.match(/^-----BEGIN PGP SIGNATURE-----/);
      expect(bodyParts[0].content).to.not.be.empty;
      expect(bodyParts[0].content).to.eql([{type: 'text', content: 'this is some signed stuff!'}]);
      expect(bodyParts[0].raw).to.not.exist;
    });
  });
});
