#!/bin/bash

if [ $TRAVIS_PULL_REQUEST == "false" ] && [ $TRAVIS_BRANCH == "dev" ]; then
 grunt
else
 grunt prod
fi

grunt dist-cr
grunt dist-ff
grunt dist-doc

if [ $TRAVIS_BRANCH != "master" ] || [ $********************** != "true" ]; then
 echo "Not building on master branch or building a pull request -> not updating gh-pages";
 exit 0;
fi

rm -rf out || exit 0;
mkdir out;
( cd out
 git init
 git config user.name "<PERSON><PERSON><PERSON><PERSON>"
 git config user.email "<EMAIL>"
 cp -R ../build/doc/* .
 git add .
 git commit -m "Deployed to Github Pages"
 echo "Deploying to gh-pages now"
 git push --force --quiet "https://${GH_TOKEN}@${GH_REF}" master:gh-pages > /dev/null 2>&1
)
