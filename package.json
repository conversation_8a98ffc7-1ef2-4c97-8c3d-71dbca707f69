{"name": "mailvelope", "version": "6.1.0", "description": "Mailvelope is a browser extension for Google Chrome and Firefox that allows secure email communication based on the OpenPGP standard. It can be configured to work with arbitrary Webmail provider.", "scripts": {"test": "grunt test", "test-dev": "grunt test-dev", "test-debug": "grunt test-debug"}, "repository": {"type": "git", "url": "https://github.com/mailvelope/mailvelope.git"}, "keywords": ["gpg"], "license": "AGPL", "readmeFilename": "Readme.md", "dependencies": {"@openpgp/web-stream-tools": "0.1.3", "autocrypt": "0.10.0", "bootstrap": "4.6.2", "clean-insights-sdk": "2.6.2", "date-fns": "3.6.0", "dompurify": "3.2.5", "emailjs-mime-builder": "github:mailvelope/emailjs-mime-builder#bf4cea3bdf50bb26b0f86ee9b1e9ee7063b5a8cc", "emailjs-mime-parser": "2.0.7", "gpgmejs": "github:mailvelope/gpgmejs#663e750b9e6ee487863d0028fbcf891cd12d3f58", "jquery": "3.7.1", "linkifyjs": "2.1.9", "openpgp": "^5.11.3", "prop-types": "15.8.1", "qrcode.react": "3.1.0", "react": "16.14.0", "react-datepicker": "6.9.0", "react-dom": "16.14.0", "react-router-dom": "5.2.0", "react-tag-input": "6.5.4", "reactstrap": "8.10.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-react": "^7.27.1", "@blakedarlin/sass-json-importer": "^1.1.0", "babel-loader": "^10.0.0", "babel-plugin-rewire": "^1.2.0", "chai": "^4.5.0", "chai-as-promised": "^8.0.1", "chai-enzyme": "^1.0.0-beta.1", "css-loader": "^7.1.2", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "grunt": "^1.6.1", "grunt-bump": "^0.8.0", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-compress": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-eslint": "^24.3.0", "grunt-jsdoc": "^2.4.1", "grunt-replace": "^2.0.2", "grunt-shell": "^4.0.0", "grunt-webpack": "^7.0.0", "ink-docstrap": "^1.3.2", "karma": "^6.4.4", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "^5.0.1", "mini-css-extract-plugin": "^2.9.2", "mocha": "^11.2.2", "sass": "^1.88.0", "sass-loader": "^16.0.5", "sinon": "^20.0.0", "string-replace-loader": "^3.1.0", "style-loader": "^4.0.0", "web-ext": "^8.6.0", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}, "optionalDependencies": {"sass-embedded": "^1.88.0"}, "overrides": {"enzyme": {"cheerio": "1.0.0-rc.6"}}, "engines": {"node": ">=18", "npm": ">=9"}, "private": true}