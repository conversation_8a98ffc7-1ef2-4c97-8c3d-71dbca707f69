{

  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },

  "settings": {
    "react": {
      "version": "16.8.0"
    }
  },

  "extends": "eslint:recommended",

  "env": {
    "browser": true,
    "node": true,
    "es6": true,
    "amd": true,
    "jquery": true,
    "webextensions": true
  },

  "rules": {
    "strict": ["error", "never"],
    /* possible errors */
    "no-console": 0,
    "no-empty": ["error", { "allowEmptyCatch": true }], // disallow empty block statements
    "require-atomic-updates": 0, // disallow assignments that can lead to race conditions due to usage of await or yield
    /* best practices */
    "curly": 2, // enforce consistent brace style for all control statements
    "no-return-await": 2, // disallows unnecessary return await
    "no-eval": 2, // disallow the use of eval()
    "no-extend-native": 2, // disallow extending native types
    "no-global-assign": 2, // disallow assignments to native objects or read-only global variables
    "no-implicit-coercion": 2, // disallow shorthand type conversions
    "no-implicit-globals": 2, // disallow var and named function declarations in the global scope
    "no-implied-eval": 2, // disallow the use of eval()-like methods
    "no-lone-blocks": 2, // disallow unnecessary nested blocks
    "no-unused-vars": ["error", {"ignoreRestSiblings": true}], // disallow unused variables
    "no-useless-escape": 0, // disallow unnecessary escape characters
    /* Stylistic Issues */
    "array-bracket-newline": ["warn", "consistent"], // enforce consisten line breaks after opening and before closing array brackets
    "array-bracket-spacing": 1, // enforce consistent spacing inside array brackets
    "block-spacing": 1, // enforce consistent spacing inside single-line blocks
    "brace-style": ["warn", "1tbs", { "allowSingleLine": true }], // enforce consistent brace style for blocks
    "comma-spacing": 1, // enforce consistent spacing before and after commas
    "computed-property-spacing": 1, // enforce consistent spacing inside computed property brackets
    "eol-last": 1, // enforce at least one newline at the end of files
    "func-call-spacing": 1, // require or disallow spacing between function identifiers and their invocations
    "indent": ["warn", 2, {"MemberExpression": 0, "SwitchCase": 1}], // enforce consistent indentation
    "key-spacing": ["warn", { "mode": "minimum" }], // enforce consistent spacing before and after keywords
    "keyword-spacing": 1, // enforce consistent spacing between keys and values in object literal properties
    "linebreak-style": 1, // enforce consistent linebreak style
    "lines-between-class-members": ["warn", {"enforce": [{"blankLine": "always", "prev": "*", "next": "method"}, {"blankLine": "never", "prev": "field", "next": "field"}]}], // require or disallow an empty line between class members
    "new-parens": ["warn"], // require parens when invoking constructors
    "no-multiple-empty-lines": ["warn", {"max": 1}], // disallow multiple empty lines
    "no-trailing-spaces": 1, // disallow trailing whitespace at the end of lines
    "no-var": 1, // require let or const instead of var
    "object-curly-newline": ["warn", {"consistent": true}], // enforce consistent line breaks inside braces
    "object-curly-spacing": ["warn", "never"], // enforce consistent spacing inside braces
    "one-var": ["warn", "never"], // enforce variables to be declared either together or separately in functions
    "padded-blocks": ["warn", "never"], // require or disallow padding within blocks
    "prefer-object-spread": 1, // disallow using Object.assign with an object literal as the first argument and prefer the use of object spread instead.
    "quotes": ["warn", "single", {"avoidEscape": true}], // enforce the consistent use of single quotes
    "semi": ["warn", "always"], // require or disallow semicolons instead of ASI
    "semi-spacing": 1, // enforce consistent spacing before and after semicolons
    "space-before-blocks": 1, // enforce consistent spacing before blocks
    "space-before-function-paren": ["warn", { "anonymous": "never", "named": "never", "asyncArrow": "always" }], // enforce consistent spacing before function definition opening parenthesis
    "space-in-parens": ["warn", "never"], // enforce consistent spacing inside parentheses
    "space-infix-ops": 1, // require spacing around operators
    /* ES6 */
    "arrow-body-style": ["warn", "as-needed"], // require braces around arrow function bodies
    "arrow-parens": ["warn", "as-needed"], // require parentheses around arrow function arguments
    "arrow-spacing": 1, // enforce consistent spacing before and after the arrow in arrow functions
    "no-useless-constructor": 1, // disallow unnecessary constructors
    "object-shorthand": ["warn", "always", {"avoidQuotes": true}], // require or disallow method and property shorthand syntax for object literals
    "prefer-arrow-callback": ["warn", {"allowNamedFunctions": true}], // require arrow functions as callbacks
    "prefer-const": ["warn", {"destructuring": "all"}], // require const declarations for variables that are never reassigned after declared
    "prefer-template": 1, // require template literals instead of string concatenation
    "template-curly-spacing": ["warn", "never"], // require or disallow spacing around embedded expressions of template strings
    "no-template-curly-in-string": "warn"
  }

}
