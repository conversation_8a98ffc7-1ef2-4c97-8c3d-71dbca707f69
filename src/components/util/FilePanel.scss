/**
 * Copyright (C) 2018-2019 Mailvelope GmbH
 * Licensed under the GNU Affero General Public License version 3
 */
@import "../../res/styles/_required";

.file-panel {
  .file-element {
    display: inline-flex;
    flex-direction: column;
    padding: 0.45rem 0.5rem;
    margin-bottom: 0.5rem;
    background-color: $white;
    box-shadow: $box-shadow-sm;
    border-radius: $border-radius-sm;
    margin-right: 0.375rem;
    font-size: $font-size-sm;

    .file-header {
      display: inline-flex;
      align-items: center;
      cursor: default;
      line-height: 1;
      > img {
        margin-right: -0.875rem;
        z-index: 1;
        + .file-extension {
          padding-left: 1.375rem;
        }
      }

      .file-extension {
        display: inline-block;
        font-weight: 500;
        border: solid 1px $border-color;
        border-radius: 4px;
        background-color: $gray-200;
        padding: 0.3rem 0.5rem;
        text-transform: uppercase;
      }

      .file-name {
        font-family: $headings-font-family;
        text-decoration: none;
        white-space: nowrap;
        display: inline-block;
        margin: 0 0.5rem;
      }

      .icon {
        font-size: $font-size-base;
        color: $gray-900;
        cursor: pointer;
      }
    }

    a.file-header {
      color: inherit;
      text-decoration: none;
      cursor: pointer;
    }

    textarea {
      background-color: $white;
      margin-top: 0.45rem;
      width: 100%;
      resize: none;
      @include media-breakpoint-up(sm) {
        min-width: 405px;
      }
      @include media-breakpoint-up(md) {
        min-width: 585px;
      }
      @include media-breakpoint-up(lg) {
        min-width: 825px;
      }
      @include media-breakpoint-up(xl) {
        min-width: 1005px;
      }
    }

  }
}
