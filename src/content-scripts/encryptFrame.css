.m-encrypt-frame[id^="eFrame"] {
  position: absolute;
  top: 5px;
  display: flex;
  visibility: hidden;
  opacity: 0;
  z-index: 3;
  transition: visibility 0.3s 0s, opacity 0.3s ease-out;
}

.m-encrypt-frame.m-show[id^="eFrame"] {
  visibility: visible;
  opacity: 1;
}

.m-encrypt-frame {
  margin-left: 10px;
}

.m-encrypt-container {
  font-family: CeraPro, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 500;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.3);
  border-radius: 24px;
  padding: 6px;
  margin-left: auto;
}

.m-encrypt-container.active {
  box-shadow: 0 0 0 3px rgba(227,0,72,0.25);
}

.m-encrypt-container .m-encrypt-button:hover span,
.m-encrypt-container.active .m-encrypt-button span {
  max-width: 250px;
  margin-left: 0.5rem;
  transition: max-width 0.25s ease-in-out, margin 0s;
}

.m-encrypt-container a {
  color: inherit !important;
  text-decoration: none !important;
  cursor: pointer;
}

.m-encrypt-container .m-encrypt-button {
  display: flex;
  align-items: center;
}

.m-encrypt-container .m-encrypt-button span {
  max-width: 0px;
  white-space: nowrap;
  overflow: hidden;
  margin-left: 0;
  margin-right: 0;
  font-size: 14px;
  font-weight: 500;
  transition: margin 0s 0.25s, max-width 0.25s ease-in-out;
}

.m-encrypt-container button.m-encrypt-close {
  padding: 6px;
  border-radius: 100%;
  border: 0;
  background-color: transparent;
  margin-left: 0.5rem;
}

.m-encrypt-container button.m-encrypt-close:focus {
  outline: none;
}

.m-encrypt-container button.m-encrypt-close:hover {
  background-color: #ebebeb;
}

.m-encrypt-container button.m-encrypt-close:active {
  background-color: #dbdbdb;
}

.m-encrypt-container .m-encrypt-close .icon-close {
  font-family: inherit !important;
  display: flex;
  align-items: center;
}

.m-encrypt-container .m-encrypt-close .icon-close::before {
  display: inline-block;
  height: 14px;
  width: 14px;
  content: url("data:image/svg+xml,%3Csvg viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 6.1534l3.2028-3.4164a.75.75 0 0 1 1.0944 1.026L8.028 7.25l3.269 3.487a.75.75 0 1 1-1.0943 1.026L7 8.3466 3.7972 11.763a.75.75 0 0 1-1.0944-1.026L5.972 7.25 2.7028 3.763a.75.75 0 0 1 1.0944-1.026L7 6.1534z' fill='%23757575' fill-rule='evenodd'/%3E%3C/svg%3E");
}
