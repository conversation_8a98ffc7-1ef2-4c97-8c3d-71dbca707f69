.m-extract-frame {
  transition: visibility 0.3s 0s, opacity 0.3s ease-out;
  background-color: rgba(255,255,255,.92);
  background-image: linear-gradient(to bottom, rgba(255,255,255,.25), rgba(255,255,255,.92) 50%, rgba(242,242,242,.92) 50%, rgba(255,255,255,.25));
  background-size: 100% 48px;
  background-position: center center;
  background-repeat: no-repeat;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  position: absolute;
  z-index: 2;
  visibility: hidden;
  opacity: 0;
  overflow: hidden;
}

.m-extract-frame.m-large {
  background-position: center 150px;
}

.m-extract-frame::before {
  position: absolute;
  width: 64px;
  height: 64px;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%,-50%,0);
  content: url("data:image/svg+xml,%3Csvg width='62px' height='64px' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Ccircle cx='16' cy='16' r='16' fill='%23FF004F'/%3E%3Cpath d='M15.995 28.667c-3.39 0-6.57-1.311-8.955-3.691-2.387-2.383-3.704-5.567-3.707-8.966a12.628 12.628 0 0 1 .592-3.836l.007-.028c.087-.306.194-.6.318-.875.022-.055.047-.116.073-.176.11-.251.545-1.115 1.588-1.77.943-.593 1.77-.644 1.866-.648.228-.027.464-.04.699-.04 1.07 0 2.015.423 2.662 1.194.492.587.76 1.307.78 2.097a4.321 4.321 0 0 1 1.959-.481c1.07 0 2.016.424 2.662 1.194.039.046.076.094.113.142.859-.852 1.993-1.336 3.14-1.336 1.07 0 2.015.424 2.662 1.194.656.782.913 1.81.722 2.893l-.672 3.807c-.09.513.017.982.301 1.321.274.327.696.507 1.187.507 1.482 0 2.003-1.08 2.345-2.246.293-1.033.428-2.107.401-3.191a10.675 10.675 0 0 0-3.219-7.387 10.683 10.683 0 0 0-7.445-3.086H16c-2.14 0-4.209.63-5.982 1.825a.97.97 0 0 1-.544.167.958.958 0 0 1-.729-.335L8.74 6.91a.96.96 0 0 1 .196-1.418 12.585 12.585 0 0 1 7.317-2.156 12.604 12.604 0 0 1 8.65 3.67 12.601 12.601 0 0 1 3.758 8.612 12.664 12.664 0 0 1-.41 3.606h.001l-.043.158-.019.063a12.57 12.57 0 0 1-.4 1.187c-.079.187-.518 1.143-1.599 1.822-.935.588-1.673.618-1.76.62a4.89 4.89 0 0 1-.439.02c-1.07 0-2.016-.424-2.662-1.194-.656-.783-.913-1.81-.722-2.893l.672-3.808c.09-.512-.017-.982-.301-1.32-.274-.327-.696-.507-1.187-.507-1.166 0-2.325.99-2.531 2.162l-.735 3.998a.528.528 0 0 1-.52.432h-.883a.527.527 0 0 1-.52-.623l.762-4.144c.09-.51-.017-.98-.3-1.319-.275-.326-.697-.506-1.188-.506-1.165 0-2.324.99-2.531 2.162l-.734 3.998a.528.528 0 0 1-.52.432H9.21a.526.526 0 0 1-.52-.623l.764-4.159.512-2.799c.09-.509-.018-.976-.302-1.315-.274-.327-.696-.507-1.187-.507-1.21 0-1.989.465-2.454 1.463a10.662 10.662 0 0 0-.755 4.408c.108 2.737 1.266 5.313 3.26 7.252 1.995 1.939 4.603 3.024 7.343 3.057H16c2.266 0 4.435-.7 6.272-2.026a.942.942 0 0 1 .555-.18.962.962 0 0 1 .565 1.743 12.571 12.571 0 0 1-7.397 2.389' fill='%23FFF2F6'/%3E%3C/g%3E%3C/svg%3E");
}

.m-extract-frame > p {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%,-50%,0);
  margin-top: 70px;
  color: #dbdbdb;
  font-family: CeraPro, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  transition: color .2s ease-out;
}

.m-extract-frame.m-large::before,
.m-extract-frame.m-large > p {
  top: 174px;
}

.m-extract-frame.m-cursor > p {
  color: #404040;
}

.m-extract-frame.m-decrypt, .m-extract-frame.m-import {
  top: 0;
}

.m-extract-frame.m-verify {
  bottom: 0;
}

.m-extract-frame.m-cursor {
  cursor: pointer;
}

.m-extract-frame .m-frame-dialog {
  position: absolute;
}

.m-extract-frame.m-show {
  visibility: visible;
  opacity: 1;
}

.m-frame-close {
  float: right;
  font-size: 20px;
  font-weight: bold;
  line-height: 18px;
  color: #000000;
  text-shadow: 0 1px 0 #ffffff;
  opacity: 0.2;
  position: relative;
  right: 3px;
  z-index: 1;
  text-decoration: none !important;
  font-family: arial, sans-serif;
}

.m-frame-close:hover {
  color: #000000;
  opacity: 0.4;
  cursor: pointer !important;
  text-decoration: none !important;
}
