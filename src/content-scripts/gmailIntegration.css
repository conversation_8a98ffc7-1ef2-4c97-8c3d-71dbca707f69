.mv-editor-btn-container {
  display: flex;
}

.mv-editor-btn {
  cursor: pointer;
  line-height: 0;
}

.mv-editor-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  padding: 8px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
  transition: box-shadow .08s linear,min-width .15s cubic-bezier(0.4,0.0,0.2,1);
}

.mv-editor-btn:hover .mv-editor-btn-content,
.mv-editor-btn:focus .mv-editor-btn-content {
  box-shadow: 0 1px 3px 0 rgba(60,64,67,0.302), 0 4px 8px 3px rgba(60,64,67,0.149);
  background-color: #fafafb;
}
.mv-editor-btn:active .mv-editor-btn-content {
  background-color: #f1f3f4;
}

.mv-reply-btn-top {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin: 0 0 0 20px;
  border: none;
  outline: none;
  cursor: pointer;
}

.mv-reply-btn-top::before {
  content: '';
  display: block;
  opacity: 0;
  position: absolute;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(0.4,0.0,0.2,1);
  bottom: -10px;
  left: -10px;
  right: -10px;
  top: -10px;
  background-color: rgba(32,33,36,0.059);
  border-radius: 50%;
  box-sizing: border-box;
  transform: scale(0);
  transition-property: transform,opacity;
}

.mv-reply-btn-top:hover::before {
  opacity: 1;
  transform: scale(1);
}

.mv-reply-btn-top::after {
  content: '';
  display: block;
  position: absolute;
  z-index: 1;
  height: 40px;
  width: 40px;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='12' fill='%23ff004f'/%3E%3Cpath fill='%23fff' d='M10 9V5l-7 7 7 7v-4.1c5 0 8.5 1.6 11 5.1-1-5-4-10-11-11z'/%3E%3C/svg%3E");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}

.mv-menu-item {
  position: relative;
  padding: 6px 48px 6px 48px;
  cursor: pointer;
}

.mv-menu-item:hover {
  background-color: #eee;
}

.mv-menu-item::before {
  content: '';
  position: absolute;
  display: inline-block;
  position: absolute;
  height: 20px;
  width: 20px;
  margin-right: 12px;
  left: 16px;
  vertical-align: middle;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}

.mv-menu-item-reply::before,
.mv-action-btn-bottom-reply::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='12' fill='%23ff004f'/%3E%3Cpath fill='%23fff' d='M10 9V5l-7 7 7 7v-4.1c5 0 8.5 1.6 11 5.1-1-5-4-10-11-11z'/%3E%3C/svg%3E");
}

.mv-menu-item-replyAll::before,
.mv-action-btn-bottom-replyAll::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='Ebene_1' viewBox='0 0 24 24'%3E%3Cstyle id='style2'%3E%3C/style%3E%3Ccircle id='circle4-6' cx='12' cy='12' r='12' fill='%23ff004f'/%3E%3Cpath id='circle4' fill='%23fff' d='M7 4.5L.0273 11.4727a12 12 0 0 0-.0039.0507L7 18.5v-3l-4-4 4-4v-3zm6 0l-7 7 7 7v-4.0996c4.084 0 7.1548 1.0884 9.5098 3.3867a12 12 0 0 0 .6113-1.2988C21.6109 12.6357 18.5778 9.2968 13 8.5v-4z'/%3E%3C/svg%3E");
}

.mv-menu-item-forward::before,
.mv-action-btn-bottom-forward::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='12' fill='%23ff004f'/%3E%3Cpath fill='%23fff' d='M12 8V4l8 8-8 8v-4H4V8h8z'/%3E%3C/svg%3E");
}


.mv-action-btns-bottom {
  align-items: center;
  display: flex;
  height: auto;
  line-height: 20px;
  padding: 0;
}

.mv-action-btn-bottom {
  align-items: center;
  border: none;
  display: inline-flex;
  justify-content: center;
  outline: none;
  position: relative;
  z-index: 0;
  font-family: 'Google Sans',Roboto,RobotoDraft,Helvetica,Arial,sans-serif;
  font-size: .875rem;
  letter-spacing: .25px;
  background: none;
  border-radius: 4px;
  box-sizing: border-box;
  color: #5f6368;
  cursor: pointer;
  font-weight: 500;
  height: 36px;
  outline: none;
  padding: 0 16px;
  box-shadow: inset 0 0 0 1px #dadce0;
  min-width: 104px;
  padding-left: 12px;
  user-select: none;
  margin-right: 12px;
  text-decoration: none;
}

.mv-action-btn-bottom:hover {
  background-color: rgba(32,33,36,0.059);
}

.mv-action-btn-bottom:active {
  box-shadow: 0 1px 2px 0 rgba(60,64,67,0.302), 0 1px 3px 1px rgba(60,64,67,0.149);
  background-color: rgba(32,33,36,0.059);
}

.mv-action-btn-bottom:focus {
  background-color: rgba(32,33,36,0.122);
  box-shadow: inset 0 0 0 1px #bdc1c6;
}

.mv-action-btn-bottom::before {
  content: '';
  height: 20px;
  display: block;
  margin-right: 8px;
  width: 20px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 20px;
}
