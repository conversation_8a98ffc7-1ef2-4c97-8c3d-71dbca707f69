/**
 * Copyright (C) 2012-2019 Mailvelope GmbH
 * Licensed under the GNU Affero General Public License version 3
 */

import {dataURL2base64, MvError} from '../lib/util';
import * as defaults from './defaults';
import * as prefs from './prefs';
import * as pwdCache from './pwdCache';
import * as uiLog from './uiLog';
import {getKeyringWithPrivKey} from './keyring';
import {getUserInfo} from './key';
import * as trustKey from './trustKey';
import {updateKeyBinding, init as initKeyBinding} from './keyBinding';
import {COMMUNICATION, recordOnboardingStep} from '../lib/analytics';

let modelInitDone;
export const modelInitialized = new Promise(resolve => modelInitDone = resolve);

export async function init() {
  pwdCache.initSession();
  await defaults.init();
  await prefs.init();
  pwdCache.init();
  initKeyBinding();
  await trustKey.init();
  modelInitDone();
}

/**
 * Decrypt armored PGP message
 * @param  {String} options.armored - armored PGP message
 * @param  {String} options.keyringId
 * @param  {Function} options.unlockKey - callback to unlock key
 * @param  {String|Array} options.senderAddress - email address of sender, used to indentify key for signature verification
 * @param  {Boolean} options.selfSigned - message is self signed (decrypt email draft scenario)
 * @return {Promise<Object>} - decryption result {data: String, signatures: Array}
 */
export async function decryptMessage({armored, keyringId, unlockKey, senderAddress, selfSigned, uiLogSource}) {
  const keyring = await getKeyringWithPrivKey(null, keyringId);
  if (!keyring) {
    throw new MvError('No GPG keyring available', 'NO_GPG_KEYRING');
  }
  try {
    let {data, signatures} = await keyring.getPgpBackend().decrypt({armored, keyring, unlockKey: options => unlockKey(options)});
    await logDecryption(uiLogSource, keyring, [], senderAddress);
    if (selfSigned) {
      // filter out foreign signatures
      signatures = signatures.filter(sig => keyring.getPrivateKeyByIds(sig.fingerprint || sig.keyId));
    }
    await updateKeyBinding(keyring, senderAddress, signatures);
    await addSignatureDetails({signatures, keyring, senderAddress});
    return {data, signatures};
  } catch (e) {
    console.log('getPgpBackend().decrypt() error', e);
    throw e;
  }
}

/**
 * Add signing key details to signature. Validate if sender identity matches signature.
 * @param {Array} signatures
 * @param {KeyringBase} keyring
 */
async function addSignatureDetails({signatures = [], keyring, senderAddress}) {
  let senderKeys;
  if (senderAddress) {
    // valid sender keys for verification of the message are keys with the sender email address as user ID
    ({[senderAddress]: senderKeys} = await keyring.getKeyByAddress(senderAddress));
  }
  for (const signature of signatures) {
    if (signature.valid === null) {
      continue;
    }
    const signingKey = keyring.keystore.getKeysForId(signature.fingerprint ?? signature.keyId, true);
    if (signingKey) {
      [signature.keyDetails] = await mapKeys(signingKey);
    }
    if (!signature.valid) {
      continue;
    }
    if (senderKeys) {
      if (!senderKeys.length) {
        // we don't have the sender email and therefore the connection between this signature and the sender is uncertain
        signature.uncertainSender = true;
      } else if (!senderKeys.some(key => key.getKeys(keyIDfromHex(signature)).length)) {
        // sender email is not present in user ID of key that created this signature
        signature.senderMismatch = true;
      }
    }
  }
}

/**
 * Log decryption operation
 * @param  {String} source - source that triggered encryption operation
 * @param {KeyringBase} keyring
 * @param  {Array<String>} keyIds - ids of used keys
 * @param  {String|Array} [senderAddress] - email address of sender, used to record keyserver-sent mail.
 */
async function logDecryption(source, keyring, keyIds, senderAddress) {
  if (source) {
    // For GPG backend, simplified logging
    uiLog.push(source, 'security_log_decryption_operation', ['GPG Key'], false);
    recordOnboardingStep(COMMUNICATION, 'Decryption');
  }
}

/**
 * Decrypt File
 * @param  {Object} encryptedFile - {content, name} with contant as dataURL and name as filename
 * @param  {Function} unlockKey - callback to unlock key
 * @return {Object<data, signatures, filename>} - data as JS binary string
 */
export async function decryptFile({encryptedFile, unlockKey, uiLogSource}) {
  try {
    const keyring = await getKeyringWithPrivKey(null);
    if (!keyring) {
      throw new MvError('No GPG keyring available', 'NO_GPG_KEYRING');
    }
    const result = await keyring.getPgpBackend().decrypt({base64: () => dataURL2base64(encryptedFile.content), keyring, unlockKey: options => unlockKey(options), format: 'binary'});
    await logDecryption(uiLogSource, keyring, []);
    if (!result.filename) {
      result.filename = encryptedFile.name.slice(0, -4);
    }
    await addSignatureDetails({signatures: result.signatures, keyring});
    return result;
  } catch (error) {
    console.log('pgpModel.decryptFile() error', error);
    throw error;
  }
}
