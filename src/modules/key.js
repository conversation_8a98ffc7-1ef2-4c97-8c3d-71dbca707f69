/**
 * Copyright (C) 2018 Mailvelope GmbH
 * Licensed under the GNU Affero General Public License version 3
 */

import {goog} from './closure-library/closure/goog/emailaddress';
import * as l10n from '../lib/l10n';
import {KEY_STATUS} from '../lib/constants';
import {isKeyPseudoRevoked} from './trustKey';

/**
 * Get primary or first available user id, email and name of key
 * @param  {Object} key - simplified key object for GPG
 * @param  {Boolean} [allowInvalid=false] - allow invalid user IDs
 * @param  {Boolean} [strict=false] - only the valid primary user is considered
 * @return {Object<userId, email, content>}
 */
export async function getUserInfo(key, {allowInvalid = false, strict = false} = {}) {
  // For GPG backend, return simplified user info
  if (key && key.userId) {
    const result = {userId: key.userId, name: key.name || '', email: key.email || ''};
    parseUserId(result);
    return result;
  }
  return {userId: l10n.get('keygrid_invalid_userid'), email: '', name: ''};
}

export function parseUserId(user) {
  user.name ??= '';
  user.email ??= '';
  if (user.name || user.email) {
    // user ID already parsed correctly by OpenPGP.js
    return;
  }
  try {
    const emailAddress = goog.format.EmailAddress.parse(user.userId);
    if (emailAddress.isValid()) {
      user.email = emailAddress.getAddress();
    } else {
      user.email = '';
    }
    user.name = emailAddress.getName();
  } catch (e) {}
  if (!user.name && !user.email) {
    user.name = l10n.get('keygrid_invalid_userid');
  }
}

export function formatEmailAddress(email, name) {
  const emailAddress = new goog.format.EmailAddress(email, name);
  return emailAddress.toString();
}

export async function verifyPrimaryKey(key) {
  // For GPG backend, assume keys are valid
  return KEY_STATUS.valid;
}

export async function verifyUser(user) {
  // For GPG backend, assume users are valid
  return KEY_STATUS.valid;
}

export async function verifySubKey(subKey) {
  // For GPG backend, assume subkeys are valid
  return KEY_STATUS.valid;
}

export function mapKeys(keys) {
  return Promise.all(keys.map(async key => {
    let uiKey = {};
    // For GPG backend, use simplified key mapping
    uiKey.type = key.type || 'public';
    uiKey.status = KEY_STATUS.valid;
    uiKey.validity = true;
    uiKey.keyId = key.keyId || 'UNKNOWN';
    uiKey.fingerprint = key.fingerprint || 'UNKNOWN';

    const userInfo = await getUserInfo(key, {allowInvalid: true});
    uiKey = {...uiKey, ...userInfo};
    uiKey.exDate = key.exDate || null;
    uiKey.crDate = key.crDate || new Date().toISOString();
    uiKey.algorithm = key.algorithm || 'UNKNOWN';
    uiKey.bitLength = key.bitLength || 'UNKNOWN';
    return uiKey;
  }));
}

function getAlgorithmString({algorithm, curve}) {
  // Simplified for GPG backend
  return algorithm || 'UNKNOWN';
}

function getKeyBitLength({bits, curve}) {
  // Simplified for GPG backend
  return bits || 'UNKNOWN';
}

export async function mapSubKeys(subkeys = [], toKey, key) {
  // Simplified for GPG backend
  toKey.subkeys = subkeys || [];
}

export async function mapUsers(users = [], toKey, keyring, key) {
  // Simplified for GPG backend
  toKey.users = users || [];
}



export async function verifyUserCertificate(user, certificate, key) {
  // Simplified for GPG backend
  return KEY_STATUS.valid;
}

/**
 * Get most recent created date field of all packets in the key
 * @param  {Object} key - simplified key object for GPG
 * @return {Date}
 */
export function getLastModifiedDate(key) {
  return key.lastModified || new Date();
}

/**
 * Check if this key is valid and can be used for encryption
 * @param  {Object}  key - simplified key object for GPG
 * @param  {String} - [keyringId] - if keyring is provided, pseudo-revoked status is checked
 * @return {Boolean}
 */
export async function isValidEncryptionKey(key, keyringId) {
  // For GPG backend, assume keys are valid for encryption
  return true;
}

export function equalKey(key1, key2) {
  return key1.fingerprint === key2.fingerprint;
}

export function toPublic(key) {
  // For GPG backend, return the key as-is
  return key;
}

/**
 * Filter out invalid keys and user IDs
 * @param  {Object} key - simplified key object for GPG
 * @return {Object|null}       the sanitized key or null if is invalid
 */
export async function sanitizeKey(key) {
  // For GPG backend, assume keys are already sanitized
  return key;
}

export function removeHexPrefix(keyId) {
  if (/^0x/.test(keyId)) {
    return keyId.substring(2);
  }
  return keyId;
}

export function keyIDfromHex({keyId, fingerprint}) {
  // Simplified for GPG backend
  return keyId || fingerprint?.slice(-16);
}
