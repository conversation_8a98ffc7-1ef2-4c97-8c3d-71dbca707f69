{"version": "@@mvelo_version", "watch_list": [{"active": true, "site": "GMX", "https_only": true, "prevent_landingpage": true, "frames": [{"scan": true, "frame": "*.gmx.net", "api": true}, {"scan": true, "frame": "*.gmx.com", "api": true}, {"scan": true, "frame": "*.gmx.co.uk", "api": true}, {"scan": true, "frame": "*.gmx.fr", "api": true}, {"scan": true, "frame": "*.gmx.es", "api": true}]}, {"active": true, "site": "WEB.DE", "https_only": true, "prevent_landingpage": true, "frames": [{"scan": true, "frame": "*.web.de", "api": true}]}, {"active": true, "site": "mail.google.com", "https_only": true, "frames": [{"scan": true, "frame": "*.mail.google.com", "api": false}]}, {"active": true, "site": "mail.live.com", "https_only": true, "frames": [{"scan": true, "frame": "*.mail.live.com", "api": false}, {"scan": true, "frame": "*.outlook.live.com", "api": false}]}, {"active": true, "site": "outlook.office.com", "https_only": true, "frames": [{"scan": true, "frame": "*.outlook.office.com", "api": false}, {"scan": true, "frame": "*.outlook.office365.com", "api": false}]}, {"active": true, "site": "mail.riseup.net", "https_only": true, "frames": [{"scan": true, "frame": "*.riseup.net", "api": true}]}, {"active": true, "site": "mail.yahoo.com", "https_only": true, "frames": [{"scan": true, "frame": "*.mail.yahoo.com", "api": false}, {"scan": true, "frame": "*.mail.yahoo.net", "api": false}]}, {"active": true, "site": "freenet.de", "https_only": true, "frames": [{"scan": true, "frame": "*.freenet.de", "api": true}]}, {"active": true, "site": "posteo.de", "https_only": true, "frames": [{"scan": true, "frame": "*.posteo.de", "api": true}]}, {"active": true, "site": "mailbox.org", "https_only": true, "frames": [{"scan": true, "frame": "*.office.mailbox.org", "api": true}]}, {"active": true, "site": "mail.ru", "https_only": true, "frames": [{"scan": true, "frame": "*.mail.ru", "api": false}]}, {"active": true, "site": "<PERSON><PERSON><PERSON>", "https_only": true, "frames": [{"scan": true, "frame": "*.mail.zoho.com", "api": false}, {"scan": true, "frame": "*.mail.zoho.eu", "api": false}]}, {"active": true, "site": "1und1 De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.de-mail.1und1.de", "api": true}]}, {"active": true, "site": "GMX De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.de-mail.gmx.net", "api": true}]}, {"active": true, "site": "Telekom De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.de-mail.t-online.de", "api": true}]}, {"active": true, "site": "T-Systems De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.de-mail.t-systems.de", "api": true}]}, {"active": true, "site": "WEB.DE De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.de-mail.web.de", "api": true}]}, {"active": true, "site": "FP Mentana-Claimsoft De-Mail", "https_only": true, "frames": [{"scan": true, "frame": "*.fp-demail.de", "api": true}]}, {"active": false, "site": "Mailvelope Demo", "https_only": true, "frames": [{"scan": true, "frame": "*.demo.mailvelope.com", "api": true}]}], "preferences": {"security": {"display_decrypted": "inline", "hide_armored_header": false, "password_cache": true, "password_timeout": 30}, "general": {"auto_add_primary": true, "auto_sign_msg": true, "prefer_gnupg": true}, "provider": {"gmail_integration": true}, "keyserver": {"autocrypt_lookup": false, "key_binding": true, "mvelo_tofu_lookup": true, "oks_lookup": true, "wkd_blacklist": ["^gmail\\.com$", "^googlemail\\.com$", "^gmx\\..{2,3}$", "^outlook\\.com$", "^hotmail\\.com$", "^web\\.de$", "^yahoo\\..{2,3}$"], "wkd_lookup": true}}}