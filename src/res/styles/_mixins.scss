@mixin button-variant-custom($color, $background, $border-color: theme-color-level($color, 2), $box-shadow: inset 0 -1px 0 theme-color-level($color, 2)) {
  border-color: $border-color;
  @include box-shadow($box-shadow);

  @include hover {
    @include gradient-bg(theme-color-level($color, 1));
    border-color: $border-color;
  }

    // Disabled comes first so active can properly restyle
  &.disabled,
  &:disabled {
    opacity: 1;
    $disabled-opacity: $btn-disabled-opacity;
    @if $color == primary {
      $disabled-opacity: $btn-primary-disabled-opacity;
    }
    color: scale-color(color-yiq($background), $lightness: ((1 - $disabled-opacity) * 100%));
    background-color: scale-color($background, $lightness: ((1 - $disabled-opacity) * 100%));
    border-color: scale-color($border-color, $lightness: ((1 - $disabled-opacity) * 100%));
    @include box-shadow(inset 0 -1px 0 scale-color($border-color, $lightness: ((1 - $disabled-opacity) * 100%)));
  }

  &:not(:disabled):not(.disabled):active,
  &:not(:disabled):not(.disabled).active,
  .show > &.dropdown-toggle {
    background-color: $border-color;
    border-color: $border-color;
  }    
}

@mixin badge-variant-custom($bg, $border, $color) {
  background-color: $bg;
  color: $color;
  border: 1px solid $border;

  @at-root a#{&} {
    @include hover-focus {
      color: $color;
    }  
  }
}

%cared-shared {
  font-family: 'MailvelopeIcons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
}

@mixin caret-down {
  @extend %cared-shared;
  content: '\e900';
}

@mixin caret-up {
  @extend %cared-shared;
  content: '\e903';
}

@mixin caret-right {
  @extend %cared-shared;
  content: '\e902';
}

@mixin caret-left {
  @extend %cared-shared;
  content: '\e901';
}
