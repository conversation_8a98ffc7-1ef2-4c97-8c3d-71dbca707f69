// import functions from BS4
@import "../../../node_modules/bootstrap/scss/functions";

// declare new variables
$font-weight-medium: 500 !default;

// overrides

// settings color
$white: #fff;
$gray-100: #fafafa; // secondary-button-bg
$gray-200: #f7f7f7; // columnheader-bg
$gray-300: #f2f2f2; // table-pressed
$gray-400: #f0f0f0; // table-divider
$gray-500: #ebebeb; // grey-hover
$gray-600: #dbdbdb; // grey86
$gray-700: #bfbfbf;
$gray-800: #949494; // prompttext
$gray-900: #757575; // grey46
$black: #404040;

$green: #2db37a;
$green-dark: #366e56; // footer-text
$red: #e30048;
$orange: #e38200;
$cyan: #8eafb1;
$dark-cyan: #098287;
$turquoise: #699496;

$primary: $red;
$secondary: $gray-200;
$info: $turquoise;
$warning: $orange;


// global options
$enable-shadows: true;
$enable-validation-icons: false;

// Settings for the `<body>` element.
$body-color: $black;

// setting fonts
$font-family-sans-serif: CeraPro, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$headings-font-family: CeraRoundPro, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";

$font-size-base: .875rem;
$h2-font-size: $font-size-base * 2.25;
$h3-font-size: $font-size-base * 1.7;
$h4-font-size: $font-size-base * 1.25;
$headings-color: $black;
$yiq-text-dark: $gray-900;

$text-muted: $gray-800;

$dt-font-weight: 400;

// Define common padding and border radius sizes and more.

$border-width: 1px;
$border-color: $gray-400;

$border-radius-sm: .25rem;
$border-radius: .5rem ;
$border-radius-lg: 1rem;

$box-shadow-sm: 0 0 4px 0 rgba($black, 0.15), 0 0 1px 0 rgba($black, 0.15);
$box-shadow-lg: 0 5px 15px 0 rgba($black, 0.2), 0 0 2px 0 rgba($black, 0.2);

$component-active-bg: $turquoise;

$caret-width: 1rem;


// buttons
// $input-btn-focus-color:       rgba($component-active-bg, .25) !default;
// $input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;
// $input-focus-border-color:              lighten($component-active-bg, 25%) !default;

$input-btn-line-height: 1;
$input-btn-line-height-sm: $input-btn-line-height;
$input-btn-line-height-lg: $input-btn-line-height;

$input-btn-padding-y: 0.635rem;
$input-btn-padding-x: 0.625rem;

$input-btn-padding-y-sm: 0.4375rem;
$input-btn-padding-x-sm: 0.625rem;


$btn-font-weight: $font-weight-medium;
$btn-disabled-opacity: 0.6;
$btn-primary-disabled-opacity: 0.4;

// forms
$input-disabled-bg: $gray-100;

// inputs
$input-color: $black;
$input-border-color: $gray-600;
$input-box-shadow: inset 0 2px 0 rgba($gray-600, .3);

$input-border-radius-sm: 0.125rem;
$input-border-radius: $border-radius-sm;
$input-border-radius-lg: $border-radius;


// custom inputs
$custom-control-indicator-border-color: $input-border-color;

// indicator input + select
$custom-control-indicator-checked-bg: $component-active-bg;
$custom-control-indicator-checked-disabled-bg:  rgba($turquoise, .5) !default;
$custom-control-indicator-checked-border-color: mix($black, $custom-control-indicator-checked-bg, 16%);
$custom-control-indicator-checked-box-shadow: inset 0 -1px 0 $custom-control-indicator-checked-border-color;
$custom-control-indicator-focus-box-shadow: $custom-control-indicator-checked-box-shadow;

$custom-control-indicator-active-bg: lighten($component-active-bg, 20%);

$custom-checkbox-indicator-border-radius: $border-radius-sm;

$custom-range-track-bg: $gray-200;
$custom-range-track-border-color: $gray-500;
// box-shadow hack
$custom-range-track-box-shadow: inset 0 2px 0 $custom-range-track-border-color, inset 0 -1px 0 $custom-range-track-border-color, inset 1px 0 0 $custom-range-track-border-color, inset -1px 0 0 $custom-range-track-border-color;
$custom-range-track-height: 0.75rem;

$custom-range-thumb-width: 1.25rem;
$custom-range-thumb-border: 1px solid $custom-control-indicator-checked-border-color;
$custom-range-thumb-box-shadow: $custom-control-indicator-checked-box-shadow;
$custom-range-thumb-active-bg: $custom-control-indicator-active-bg;

$custom-switch-size: 1.25rem;
$custom-switch-width: $custom-switch-size * 2;
$custom-switch-indicator-border-radius: $custom-switch-size / 2;
$custom-switch-indicator-size: calc(#{$custom-switch-size} - #{$border-width * 4});
$custom-switch-track-bg: $gray-200;
$custom-switch-indicator-bg: gray-600;

$custom-select-indicator-color: $gray-800;
$custom-select-indicator: str-replace(url("data:image/svg+xml,%3Csvg viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpath fill='#{$custom-select-indicator-color}' d='M8.93933983,7 L3.46966991,1.53033009 C3.1767767,1.23743687 3.1767767,0.762563133 3.46966991,0.469669914 C3.76256313,0.176776695 4.23743687,0.176776695 4.53033009,0.469669914 L10.5303301,6.46966991 C10.8232233,6.76256313 10.8232233,7.23743687 10.5303301,7.53033009 L4.53033009,13.5303301 C4.23743687,13.8232233 3.76256313,13.8232233 3.46966991,13.5303301 C3.1767767,13.2374369 3.1767767,12.7625631 3.46966991,12.4696699 L8.93933983,7 Z' transform='translate(7.000000, 7.000000) scale(1, -1) rotate(-90.000000) translate(-7.000000, -7.000000) '%3E%3C/path%3E%3C/g%3E%3C/svg%3E"), "#", "%23");


// tables
$table-color: $black;
$table-head-bg: $gray-200;
$table-head-color: $gray-900;
$table-hover-bg: $gray-100;
$table-active-bg: $gray-300;
$table-cell-padding: 0.8125rem;
$table-cell-padding-sm: 0.35rem;
$table-striped-order: even;

// alerts
$alert-padding-y: 0.8125rem;
$alert-padding-x: 1rem;
$alert-border-radius: $border-radius-sm;

$alert-bg-level: -11;
$alert-border-level: -10;
$alert-color-level: 12.5;

// navs
$navbar-light-color: $black;
$navbar-light-hover-color: $navbar-light-color;
$navbar-light-active-color: $primary;

// nav-pills;
$nav-pills-link-active-bg: $primary;

// dropdowns
$dropdown-color: $yiq-text-dark;
$dropdown-link-active-bg: $gray-300;


// tooltip
$tooltip-font-size: $font-size-base;
$tooltip-max-width: 360px;
$tooltip-opacity: 0.85;

// toasts
$toast-max-width: 360px;
$toast-background-color: rgba($black, 0.85);
$toast-color: $white;
$toast-border-width: 0;
$toast-border-radius: $border-radius-lg;
$toast-box-shadow: 0 5px 15px 0 rgba($black, 0.2), 0 0 2px 0 rgba($black, 0.2);
$toast-header-color: $white;
$toast-header-background-color: $black;

// badges
$badge-font-weight: 500;
$badge-padding-x: 0.5rem;
$badge-padding-y: 0.3175rem;
$badge-border-radius: $border-radius-sm;
$badge-font-size: 100%;

// Required from BS4
@import "../../../node_modules/bootstrap/scss/variables";
@import "../../../node_modules/bootstrap/scss/mixins";
