/**
 * Mailvelope - secure email with OpenPGP encryption for Webmail
 * Copyright (C) 2015-19 Mailvelope GmbH
 */


/* set browser styles */

::placeholder {
   font-style: italic;
}

/* general overrides */

b {
  font-weight: 700;
}

strong {
  font-weight: 500;
}

/* custom classes */

.btn-bar {
  display: flex;
  flex-wrap: wrap;
  & > .btn {
    margin-right: map-get($spacers, 2);
    margin-bottom: map-get($spacers, 1);
    &:last-child {
      margin-right: 0;
    }
  }
}

.row.btn-bar {
  flex-wrap: nowrap;
  margin-right: -1* map-get($spacers, 1);
  margin-left: -1* map-get($spacers, 1);
  > .col,
  > [class*="col-"] {
    padding-right: map-get($spacers, 1);
    padding-left: map-get($spacers, 1);
  }
}

.card-clean {
  border: 0;
  .card-header {
    padding-left: 0.5rem;
    padding-right: 0;
    background-color: transparent;
    h3 {
      margin-bottom: 0;
    }
  }
  .card-body {
    padding: 1.25rem 0.5rem;
  }
}

.card-clean-table {
  @extend .card-clean;
  .card-header {
    border-bottom: 0;
  }
}

.table-custom {
  border-bottom: $table-border-width solid $table-border-color;
  th {
    border: 0 !important;
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    font-size: 0.75rem;
    font-weight: 400;
    background-color: $gray-200;
    color: $gray-900;
    &:first-child {
      border-top-left-radius: $border-radius-sm;
      border-bottom-left-radius: $border-radius-sm;
    }
    &:last-child {
      border-top-right-radius: $border-radius-sm;
      border-bottom-right-radius: $border-radius-sm;
    }
  }
  tr {
    &:first-child {
      td {
        border-top: 0;
      }
    }

    td {
      strong {
        font-size: 1rem;
      }
    }
  }

  &.table-hover {
    cursor: pointer;

    tr {
      .actions {
        text-align: right;
        white-space: nowrap;
        > .btn {
          visibility: hidden;
        }

        > .icon {
          margin-left: 0.5rem;
        }
      }

      &:hover .actions > .btn {
        visibility: visible;
      }
    }
  }
}

button.icon-btn {
  cursor: pointer;
  color: $gray-900;
  padding: $btn-padding-y;
  border-style: none;
  border-radius: $border-radius-sm;
  background-color: transparent;
  &:hover {
    background-color: $gray-500;
  }
  &:active {
    color: darken($gray-900, 5%);
    background-color: darken($gray-500, 10%);
  }
}

  /**
   * Set custom link color in decrypt message and encrypted form sandbox component
   */
  #content, #root {
    a {
      color: $dark-cyan;
      &:hover {
        color: darken($dark-cyan, 15%);
      }
    }
  }

/* old custom classes still in use */

.termination {
  background-color: grey !important;
  background-image: none !important;
  color: lightgray;
  font-size: 196px;
  text-align: center;
  display: block;
  padding: 50px;
}

/**
 * Setup page
 */
.keyring_setup_message {
  display: none;
}

.keyring_setup_message.active {
  display: block;
}


/**
 * Secure background
 */

.container > .secureBackground {
  margin-bottom: 1.5rem;
  box-shadow: $box-shadow-lg;
}

.secureBgndSettingsBtn {
  min-width: 10px !important;
  margin-right: 0;
  width: 32px;
  height: 32px;
  padding: 0 !important;
  margin-bottom: 3px;
  color: transparent;
  background: radial-gradient(ellipse at center, rgba(245,245,245,1) 0%,rgba(245,245,245,1) 55%,rgba(245,245,245,0) 75%,rgba(245,245,245,0) 100%);
}
.secureBgndSettingsBtn:hover {
  color: transparent;
}

/* General icon for secure button */
.lockBtnIcon, .lockBtnIcon:active {
  margin: 0px;
  width: 28px;
  height: 28px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGlkPSJzZWNCZ25kIiB2ZXJzaW9uPSIxLjEiIHdpZHRoPSI3MnB4IiBoZWlnaHQ9IjcycHgiIHZpZXdCb3g9IjAgMCAyNyAyNyI+PHBhdGggdHJhbnNmb3JtPSJyb3RhdGUoMCAxNCAxNCkiIHN0eWxlPSJmaWxsOiAjODA4MDgwOyIgZD0ibSAxMy45NjM2NDksMjUuOTAxNzU0IGMgLTQuNjkwMDAwNSwwIC04LjUwMDAwMDUsLTMuNzggLTguNTAwMDAwNSwtOC40NCAwLC0xLjY0IDAuNDcsLTMuMTcgMS4yOSwtNC40NyBWIDkuMDQxNzU0NiBjIDAsLTMuOTM5OTk5MiAzLjIzLC03LjE0OTk5OTIgNy4yMDAwMDA1LC03LjE0OTk5OTIgMy45NywwIDcuMiwzLjIxIDcuMiw3LjE0OTk5OTIgdiAzLjk0OTk5OTQgYyAwLjgyLDEuMyAxLjMsMi44MyAxLjMsNC40OCAwLDQuNjUgLTMuOCw4LjQzIC04LjQ5LDguNDMgeiBtIC0xLjM1LC03Ljk5IHYgMy4zMyBoIDAgYyAwLDAuMDIgMCwwLjAzIDAsMC4wNSAwLDAuNzQgMC42MSwxLjM0IDEuMzUsMS4zNCAwLjc1LDAgMS4zNSwtMC42IDEuMzUsLTEuMzQgMCwtMC4wMiAwLC0wLjAzIDAsLTAuMDUgaCAwIHYgLTMuMzMgYyAwLjYzLC0wLjQzIDEuMDQsLTEuMTUgMS4wNCwtMS45NyAwLC0xLjMyIC0xLjA3LC0yLjM4IC0yLjQsLTIuMzggLTEuMzIsMCAtMi40LDEuMDcgLTIuNCwyLjM4IDAuMDEsMC44MiAwLjQzLDEuNTQgMS4wNiwxLjk3IHogbSA2LjI5LC04Ljg2OTk5OTQgYyAwLC0yLjcwOTk5OTIgLTIuMjIsLTQuOTA5OTk5MiAtNC45NSwtNC45MDk5OTkyIC0yLjczLDAgLTQuOTUwMDAwNSwyLjIgLTQuOTUwMDAwNSw0LjkwOTk5OTIgViAxMC42MTE3NTQgQyAxMC4zOTM2NDksOS42MjE3NTQ0IDEyLjEwMzY0OSw5LjAzMTc1NDYgMTMuOTUzNjQ5LDkuMDMxNzU0NiBjIDEuODUsMCAzLjU1LDAuNTg5OTk5OCA0Ljk0LDEuNTc5OTk5NCBsIDAuMDEsLTEuNTY5OTk5NCB6IiAvPjwvc3ZnPg==");
}

/* Clearfix */
.jumbotron.secureBackground:after {
  content: '';
  display: block;
  clear: both;
  height: 0;
}

/**
 * Smallscreen optimization
 */

@media (max-width:767px) {
  .table-responsive-custom th:nth-child(4),
  .table-responsive-custom td:nth-child(4),
  .table-responsive-custom th:nth-child(5),
  .table-responsive-custom td:nth-child(5) {
    display: none;
  }
}

/* BS4 overrides */


/* set 2rem (4rem will be divided by 2) padding fo 100% wide container at all breakpoints */
.container-fluid {
  @include make-container(4rem);
}

/* remove text underline from button links and nav-links */
a.btn,
a.nav-link {
  text-decoration: none;
}


/* decrease size of status label in card-title */
.card-title h2 > span.small {
  font-size: 70%;
}


.modal-header h3 {
  font-size: 1.125rem;
   ~ .close {
    font-size: 1rem;
    padding-top: $modal-inner-padding + 0.4rem;
  }
}

// navbar
nav {
  .navbar-brand {
    margin-right: 2rem;
  }

  .navbar-nav {
    &.navbar-tabs {
      @include media-breakpoint-up(sm) {
        flex-direction: row;
        .nav-item {
          .nav-link {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
          }
        }
      }
    }
    .nav-item {
      .nav-link {
        font-family: $headings-font-family;
        font-size: 1rem;
        font-weight: 400;
        color: $gray-900;
        position: relative;
        cursor: pointer;

        &::after {
          content:' ';
          position: absolute;
          top: 92%;
          left: 50%;
          width: 50%;
          height: 0.125rem;
          background-color: transparent;
          transform:translateX(-50%);
        }

        &:hover {
            color: $black;
        }
      }

      &:not(:last-child) {
        .nav-link {
          @include media-breakpoint-up(md) {
            margin-right: 1rem;
          }
        }
      }

      &.active .nav-link,
      .nav-link.active {
          color: theme-color("primary");
          font-weight: 500;
      }

      &.active .nav-link::after,
      .nav-link.active::after {
        @include media-breakpoint-up(md) {
          background-color: theme-color("primary");
        }
      }

    }

  }

}

// nav-pills
.nav-pills {
  .nav-link {
    display: flex;
    align-items: center;
    font-size: inherit;
    line-height: 1;
    color: $yiq-text-dark;
    padding: 0.25rem 0;
  }

  .nav-link.active {
    font-weight: 500;
    color: theme-color("primary");
    background-color: transparent;
    &::after {
      display: inline-block;
      height: 14px;
      width: 14px;
      margin-left: 0.25rem;
      @include media-breakpoint-up(sm) {
        content: str-replace(url("data:image/svg+xml,%3Csvg viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpath fill='#{theme-color("primary")}' d='M7.89429765,3.32720883 L11.0070864,9.5527864 C11.2540757,10.0467649 11.0538513,10.6474379 10.5598728,10.8944272 C10.4210174,10.9638549 10.2679043,11 10.1126592,11 L3.88708167,11 C3.33479692,11 2.88708167,10.5522847 2.88708167,10 C2.88708167,9.8447549 2.92322676,9.69164184 2.99265448,9.5527864 L6.10544327,3.32720883 C6.35243252,2.83323033 6.95310556,2.63300598 7.44708406,2.87999523 C7.64061146,2.97675893 7.79753395,3.13368142 7.89429765,3.32720883 Z' transform='translate(7.000000, 7.000000) rotate(90.000000) translate(-7.000000, -7.000000) '%3E%3C/path%3E%3C/g%3E%3C/svg%3E"), "#", "%23");
      }
      @include media-breakpoint-down(sm) {
        content: str-replace(url("data:image/svg+xml,%3Csvg viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'%3E%3Cg stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cpath fill='#{theme-color("primary")}' d='M7.89429765,3.32720883 L11.0070864,9.5527864 C11.2540757,10.0467649 11.0538513,10.6474379 10.5598728,10.8944272 C10.4210174,10.9638549 10.2679043,11 10.1126592,11 L3.88708167,11 C3.33479692,11 2.88708167,10.5522847 2.88708167,10 C2.88708167,9.8447549 2.92322676,9.69164184 2.99265448,9.5527864 L6.10544327,3.32720883 C6.35243252,2.83323033 6.95310556,2.63300598 7.44708406,2.87999523 C7.64061146,2.97675893 7.79753395,3.13368142 7.89429765,3.32720883 Z' transform='translate(7.000000, 7.000000) rotate(180.000000) translate(-7.000000, -7.000000) '%3E%3C/path%3E%3C/g%3E%3C/svg%3E"), "#", "%23");
      }
    }
  }

  .nav-link:not(:last-child),
  .show > .nav-link:not(:last-child) {
    margin-bottom: 1rem;
  }

  .nav-link:not(.active):hover,
  .show > .nav-link:not(.active):hover {
    color: $black;
  }
}



// breadcrump-item

.breadcrumb {
  margin-left: -0.5rem;
  margin-bottom: 0.5rem;

  .breadcrumb-item {
    color: theme-color("primary");
    & > a {
      line-height: 1;
      font-weight: 500;
      font-size: 0.75rem;
      text-decoration: none;
      padding: 0.5rem 0.625rem;
      border-radius: $border-radius;
      background-color: transparent;
      cursor: pointer;

      &:hover {
        color: inherit;
        background-color: rgba(theme-color("primary"),0.1);
      }

      &:active {
        color: inherit;
        background-color: rgba(theme-color("primary"),0.2);
      }

      &.active {
        color: inherit;
        background-color: rgba(theme-color("primary"),0.1);
      }
    }
  }
}

.alert {
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
  display: inline-block;
  border-left-width: 0.25rem;
}

// alerts custom css
@each $color, $value in $theme-colors {
  .alert-#{$color} {
    border-left-color: theme-color($color);
  }
}

// button custom css
@each $color, $value in $theme-colors {
  .btn-#{$color} {
    @include button-variant-custom($color, $value);
  }
}

// fix custom control
.custom-control-input {
  &:focus ~ .custom-control-label::before {
    @if $enable-shadows {
      box-shadow: $custom-control-indicator-focus-box-shadow, $input-focus-box-shadow !important;
    } @else {
      box-shadow: $input-focus-box-shadow !important;
    }
  }
  &:focus:not(:checked) ~ .custom-control-label::before {
    box-shadow: none !important;
  }
  &:checked:disabled ~ .custom-control-label::before {
    border: 1px solid darken($custom-control-indicator-checked-disabled-bg, 0.2);
    box-shadow: none !important;
  }
}

.custom-range {
  &:focus {
    border: 0 !important;
    // Pseudo-elements must be split across multiple rulesets to have an effect.
    // No box-shadow() mixin for focus accessibility.
    &::-webkit-slider-thumb { box-shadow: $custom-range-thumb-box-shadow, $custom-range-thumb-focus-box-shadow !important; }
    &::-moz-range-thumb     { box-shadow: $custom-range-thumb-box-shadow, $custom-range-thumb-focus-box-shadow !important; }
    &::-ms-thumb            { box-shadow: $custom-range-thumb-box-shadow, $custom-range-thumb-focus-box-shadow !important; }
  }

  &::-webkit-slider-thumb {
     &:active {
      border: 0 !important;
      box-shadow: $custom-range-thumb-focus-box-shadow !important;
      // @include gradient-bg($custom-range-thumb-active-bg);
    }
  }

  &::-moz-range-thumb {
    &:active {
      border: 0 !important;
      box-shadow: $custom-range-thumb-focus-box-shadow !important;
      // @include gradient-bg($custom-range-thumb-active-bg);
    }
  }

  &::-ms-thumb {
    &:active {
      border: 0 !important;
      box-shadow: $custom-range-thumb-focus-box-shadow !important;
      // @include gradient-bg($custom-range-thumb-active-bg);
    }
  }

  &:disabled {
    &::-webkit-slider-thumb {
      border: 0 !important;
      // background-color: $custom-range-thumb-disabled-bg;
    }
    &::-moz-range-thumb {
      border: 0 !important;
      // background-color: $custom-range-thumb-disabled-bg;
    }
    &::-ms-thumb {
      border: 0 !important;
      // background-color: $custom-range-thumb-disabled-bg;
    }
  }
}


.custom-switch {
  padding-left: $custom-switch-width + $custom-control-gutter;

  .custom-control-label {
    // background
    &::before {
      top: ($font-size-base * $line-height-base - $custom-switch-size) / 2;
      left: -($custom-switch-width + $custom-control-gutter);
      width: $custom-switch-width;
      height: $custom-switch-size;
      background-color: $custom-switch-track-bg;
      border: 1px solid $custom-control-indicator-border-color;
      pointer-events: all;
      // stylelint-disable-next-line property-blacklist
      border-radius: $custom-switch-indicator-border-radius;
      @include transition(transform .15s ease-in-out, $custom-forms-transition);
    }

    &::after {
      // knob
      top: calc(#{(($font-size-base * $line-height-base - $custom-switch-size) / 2)} + #{$custom-control-indicator-border-width * 2}) !important;
      left: calc(#{-($custom-switch-width + $custom-control-gutter)} + #{$custom-control-indicator-border-width * 2}) !important;
      width: $custom-switch-indicator-size;
      height: $custom-switch-indicator-size;
      background-color: $custom-switch-indicator-bg !important;
      border: 1px solid $gray-700;
      // stylelint-disable-next-line property-blacklist
      border-radius: $custom-switch-indicator-size/2;
      @if $enable-shadows {
        box-shadow: inset 0 -1px 0px $gray-700;
      } @else {
        box-shadow: none;
      }

    }
  }

  .custom-control-input:checked ~ .custom-control-label {
    &::after {
      background-color: $custom-control-indicator-checked-bg !important;
      border: 1px solid $custom-control-indicator-checked-border-color;
      @if $enable-shadows {
        box-shadow: $custom-control-indicator-checked-box-shadow !important;
      } @else {
        box-shadow: none;
      }
      transform: translateX($custom-switch-width - $custom-switch-size) !important;
    }
  }

  .custom-control-input:checked ~ .custom-control-label {
    &::before {
      background-color: rgba($custom-control-indicator-checked-bg, 0.1);
      border: 1px solid rgba($custom-control-indicator-checked-bg, 0.2);
      @if $enable-shadows {
        box-shadow: inset 0 -1px 0 rgba($custom-control-indicator-checked-bg, 0.125);
      } @else {
        box-shadow: none;
      }
    }
  }

  .custom-control-input:checked {
    &:focus ~ .custom-control-label::before {
      // the mixin is not used here to make sure there is feedback
      @if $enable-shadows {
        box-shadow: $input-focus-box-shadow !important;
      } @else {
        box-shadow: none;
      }
    }
  }

  .custom-control-input:disabled {
    &:checked ~ .custom-control-label::before {
      background-color: $custom-control-indicator-checked-disabled-bg;
    }
  }
}

// custom toast

.toast {
  &.success,
  &.error {
    max-width: 450px;
  }
  .toast-header {
    padding: 1rem 1.5rem;
  }
  .toast-body {
    padding: 1rem 1.5rem;
    &.dismissable {
      .close {
        font-size: inherit;
        float: none;
        margin-left: 1rem;
      }
    }
    > span[class^='icon-'] {
      margin-right: 0.75rem;
    }
  }
  .close {
    color: inherit;
    text-shadow: none;
  }
}


// custom badges
.badge {
  font-family: $headings-font-family;
}

@each $color, $value in $theme-colors {
  .badge-#{$color} {
    @include badge-variant-custom(theme-color($color), theme-color-level($color, 5), color-yiq(theme-color($color)));
  }
}

// description list

dt {
  color: $gray-900;
}

// Google sign in button

@font-face {
  font-family: "Roboto";
  src: url('res/fonts/Google/Roboto-Regular.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

// Analytics Consent Prompt

.round-container {
  background-color: #d0eadf; 
  width: 230px;
  height: 230px;
  border-radius: 100%;
  overflow: hidden; /* To ensure that the image does not overflow from the container */
  padding: 15px;
}
.round-container img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 50%;
}
.btn-consent {
  width: 150px;
  color: red;
  background-color: white;
  border: 1px solid red;
  transition: border-color 0.3s ease;
}
.btn-consent:hover {
  border-color: #cc0000; 
}
.accordion-control {
  font-weight: bold;
  cursor: pointer;
  background-color: #F2F4F4;
  border-radius: 80px;
  width: 35%;
  height: 43px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.collapse-icon img {
  color: #0000;
  margin-left: 10px;
  width: 10px;
}