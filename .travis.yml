language: node_js
node_js:
  - 14
cache: npm
before_install:
- npm install -g grunt-cli
install:
- npm ci
jobs:
  include:
  - name: grunt
    script:
      - grunt
      - grunt dist-cr
      - grunt dist-ff
  - name: test
    script: npm test
  - name: nightly
    stage: deploy
    script:
      - grunt
      - grunt dist-cr
      - grunt dist-ff
      - openssl aes-256-cbc -K $encrypted_5d28b41102c4_key -iv $encrypted_5d28b41102c4_iv -in .travis/crx_signing.pem.enc -out .travis/crx_signing.pem -d
      - .travis/crxmake.sh build/chrome .travis/crx_signing.pem
      - .travis/deploy-nightly.sh
    if: branch = dev AND NOT fork AND type != pull_request
  - name: github pages
    stage: deploy
    script: .travis/deploy-grunt.sh
    if: branch = master AND NOT fork
env:
  global:
  - GH_REF=github.com/mailvelope/mailvelope.git
  - secure: evnYoSpdhcwSlYS+PvMZFvh1xairXh+CUhKwzFvoccDoxSoDtZvzSibyntky0mz+UK7XC0JC515vKSqRn3zn9/4BEedGv8Xfrvc4q1+Ln5v2zO/2pmzcqXh3dWNnlpB5teyFb0nvpaB9cTtM8MPXkMqbn47OEM0sLTEayYAXAB4=
  - secure: EzXGSPLyViYC/SQyu9RZ55q4s+7jqtr2DvfFqzE65KsThFq8aAenaVkRikkWxAn0AT44a39eP29YWIABpFJdnDJCR4Gjqm0GRTpRig4kKuyTavbShST8ZPLTInklt9qpWtRS8D48+bbdjFvibpUs7YHhYMfRWVPVLRlerlp6MjQ=
  - secure: Fm4m7fFmD1NfBZdXcNCdnkRlcwvaJtKSOgUy1nkm21Wzjzx2tjxJD90j9aaxF7VeWJr3AOO7k4EN5qdtrkGi5bILYprCwujBa8wLR0etMI/SkAfCd/bLfqAglubqOaaGu//vLN0IL1wDv9HDz9DU42EbbR9vM9cmMebPctrKTy0=
deploy:
  provider: releases
  api-key: "${GH_TOKEN}"
  file: dist/mailvelope.client-api-documentation.zip
  file_glob: true
  skip_cleanup: true
  on:
    tags: true
    branch: master
addons:
  chrome: stable
  ssh_known_hosts: build.mailvelope.com
  apt:
    packages:
    - sshpass
